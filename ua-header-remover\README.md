# UA Header Remover

一个简单的Chrome浏览器扩展，自动移除所有HTTP请求中的User-Agent头部信息。

## 功能特点

- 🚫 自动移除所有HTTP请求的User-Agent头
- 🔄 支持所有类型的请求（页面、AJAX、Fetch、WebSocket等）
- ⚡ 无需配置，安装即用
- 🎯 覆盖所有网站和域名
- 📦 轻量级，无后台脚本

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展将自动启用并开始工作

## 工作原理

此扩展使用Chrome的Declarative Net Request API来拦截和修改HTTP请求头。它会：

1. 拦截所有类型的HTTP请求
2. 移除请求中的`User-Agent`头部
3. 让请求继续发送（但不包含UA信息）

## 文件结构

```
ua-header-remover/
├── manifest.json    # 扩展配置文件
├── rules.json      # 网络请求规则
└── README.md       # 说明文档
```

## 技术说明

- 使用Manifest V3规范
- 基于Declarative Net Request API
- 无需后台脚本，性能更优
- 规则在扩展安装时自动加载

## 注意事项

- 移除UA头可能导致某些网站功能异常
- 部分网站可能拒绝没有UA头的请求
- 建议在需要时临时禁用扩展

## 许可证

MIT License
